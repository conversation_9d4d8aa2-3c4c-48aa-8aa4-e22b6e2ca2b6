import { loadEnv, defineConfig } from '@medusajs/framework/utils';

loadEnv(process.env.NODE_ENV || 'development', process.cwd());

export default defineConfig({
  projectConfig: {
    databaseUrl: process.env.DATABASE_URL,
    http: {
      storeCors: process.env.STORE_CORS!,
      adminCors: process.env.ADMIN_CORS!,
      authCors: process.env.AUTH_CORS!,
      jwtSecret: process.env.JWT_SECRET || 'supersecret',
      cookieSecret: process.env.COOKIE_SECRET || 'supersecret',
    },
  },

  // ✅ REGISTER CORE MODULES HERE - Minimal configuration
  modules: {
    cart: {
      resolve: '@medusajs/cart',
      options: {},
    },
    order: {
      resolve: '@medusajs/order',
      options: {},
    },
    customer: {
      resolve: '@medusajs/customer',
      options: {},
    },
    payment: {
      resolve: '@medusajs/payment',
      options: {},
    },
    inventory: {
      resolve: '@medusajs/inventory',
      options: {},
    },
    stockLocation: {
      resolve: '@medusajs/stock-location',
      options: {},
    },
    promotion: { resolve: '@medusajs/medusa/promotion' },
  },

  // ✅ REGISTER PAYMENT PROVIDERS
  plugins: [
    {
      resolve: 'medusa-payment-manual',
      options: {
        // Cash on Delivery configuration
        name: 'Cash on Delivery',
        description: 'Pay with cash when your order is delivered',
        id: 'manual',
      },
    },
  ],
});
